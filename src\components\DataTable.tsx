import React, { useState, useEffect } from "react";
import { Search, Filter, X, Edit, Trash2, Clock, AlertCircle, CheckCircle, PlayCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Incident } from "@/contexts/IncidentContext";
import { format } from "date-fns";

interface DataTableProps {
  data: Incident[];
  onView: (incident: Incident) => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  context?: 'my-reports' | 'my-actions' | 'all-incidents';
}

const DataTable: React.FC<DataTableProps> = ({
  data,
  onView,
  onEdit,
  onDelete,
  context = 'my-reports'
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState<Incident[]>(data);
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [typeFilters, setTypeFilters] = useState<string[]>([]);

  // Get unique incident types from data
  const incidentTypes = Array.from(new Set(data.map(incident => incident.incidentType)));

  // Status options for filtering based on context
  const getStatusOptions = () => {
    const allOptions = [
      { value: "draft", label: "Draft" },
      { value: "submitted", label: "Reported" },
      { value: "under-review", label: "Under Review" },
      { value: "investigation", label: "Under Investigation" },
      { value: "closed", label: "Closed" },
    ];

    switch (context) {
      case 'my-reports':
        return allOptions;
      case 'my-actions':
        return allOptions.filter(option =>
          option.value === "submitted" || option.value === "under-review"
        );
      case 'all-incidents':
        return allOptions;
      default:
        return allOptions;
    }
  };

  const statusOptions = getStatusOptions();

  // Helper function to get incident type label
  const getIncidentTypeLabel = (type: string): string => {
    const typeMap: Record<string, string> = {
      "fall": "Fall",
      "slip": "Slip",
      "nearMiss": "Near Miss",
      "injury": "Injury",
      "environmental": "Environmental",
      "security": "Security",
      "fire": "Fire",
      "electrical": "Electrical",
      "chemical": "Chemical",
      "vehicle": "Vehicle",
      "other": "Other"
    };

    return typeMap[type] || type;
  };

  // Update filtered data when search term or filters change
  useEffect(() => {
    let result = [...data];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(incident =>
        incident.id?.toLowerCase().includes(term) ||
        incident.description?.toLowerCase().includes(term) ||
        incident.incidentType?.toLowerCase().includes(term)
      );
    }

    // Apply status filters
    if (statusFilters.length > 0) {
      result = result.filter(incident => statusFilters.includes(incident.status));
    }

    // Apply type filters
    if (typeFilters.length > 0) {
      result = result.filter(incident => typeFilters.includes(incident.incidentType));
    }

    setFilteredData(result);
  }, [data, searchTerm, statusFilters, typeFilters]);

  // Toggle status filter
  const toggleStatusFilter = (status: string) => {
    setStatusFilters(prev =>
      prev.includes(status)
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  // Toggle type filter
  const toggleTypeFilter = (type: string) => {
    setTypeFilters(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("");
    setStatusFilters([]);
    setTypeFilters([]);
  };

  // Helper function to get stage based on incident status and stage
  const getIncidentStage = (incident: Incident): string => {
    if (incident.stage) {
      return incident.stage;
    }

    // Fallback to determine stage based on status
    switch (incident.status) {
      case "under-review":
        return "Supplementary information in Progress";
      case "submitted":
        return "Preliminary Analysis in Progress";
      case "investigation":
        return "Investigation";
      case "closed":
        return "Preliminary Analysis Completed";
      case "draft":
        return "Initial Report";
      default:
        return "Initial Report";
    }
  };

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case "draft":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800">
            <Clock className="w-3 h-3 mr-1" /> Draft
          </Badge>
        );
      case "submitted":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800">
            <Clock className="w-3 h-3 mr-1" /> Reported
          </Badge>
        );
      case "under-review":
        return (
          <Badge variant="outline" className="bg-amber-100 text-amber-800">
            <Clock className="w-3 h-3 mr-1" /> Under Review
          </Badge>
        );
      case "investigation":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            <AlertCircle className="w-3 h-3 mr-1" /> Under Investigation
          </Badge>
        );
      case "closed":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" /> Completed
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Render stage badge
  const renderStageBadge = (stage: string) => {
    return (
      <Badge variant="outline" className="bg-purple-100 text-purple-800 text-xs">
        {stage}
      </Badge>
    );
  };

  // Get the title based on context
  const getContextTitle = () => {
    switch (context) {
      case 'my-reports':
        return "My Reported Incidents";
      case 'my-actions':
        return "Incidents Requiring My Action";
      case 'all-incidents':
        return "All Incidents";
      default:
        return "Incidents";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start mb-6">
        <h2 className="text-xl font-semibold">{getContextTitle()}</h2>
      </div>

      <div className="rounded-md border bg-card overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-muted/50">
              <TableRow>
                <TableHead className="font-semibold">ID</TableHead>
                <TableHead className="font-semibold">Incident Date</TableHead>
                <TableHead className="font-semibold">Incident Title</TableHead>
                <TableHead className="font-semibold">Incident Status</TableHead>
                <TableHead className="text-right font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length > 0 ? (
                filteredData.map((incident) => {
                  // For newly initiated incidents, ensure reviewer information is not displayed
                  // until the reviewer has taken action
                  const displayIncident = incident.status === 'under-review' && !incident.reviewedBy ?
                    { ...incident, incidentReviewer: "", reviewedBy: null } :
                    incident;

                  return (
                    <TableRow key={displayIncident.id} className="hover:bg-muted/30 transition-colors">
                      <TableCell className="font-medium">
                        {displayIncident.id}
                      </TableCell>
                      <TableCell>
                        {displayIncident.incidentDate && !isNaN(new Date(displayIncident.incidentDate).getTime())
                          ? format(new Date(displayIncident.incidentDate), 'MMM dd, yyyy')
                          : "N/A"}
                      </TableCell>
                      <TableCell>
                        {displayIncident.incidentTitle || displayIncident.description}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {renderStatusBadge(displayIncident.status)}
                          {renderStageBadge(getIncidentStage(displayIncident))}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {context === 'my-actions' ? (
                          <Button
                            variant="default"
                            size="sm"
                            className="bg-blue-600 hover:bg-blue-700"
                            onClick={() => onEdit(displayIncident.id)}
                            title="Take Action"
                          >
                            Take Action
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onView(displayIncident)}
                          >
                            View
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="h-32 text-center">
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                      <AlertCircle className="h-8 w-8 mb-2 opacity-40" />
                      <p>No incidents found.</p>
                      <p className="text-sm">Try adjusting your filters or search criteria.</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default DataTable;
